############################## MacOS  ##############################
.DS_Store
._.DS_Store
Thumbs.db

############################## IDE    ##############################
.vscode/
*.vs

# IntelliJ
*.iml
.idea/

.settings/
.project/
.classpath

cmake-build-debug
cmake-build-releas

############################## Out  ################################
bin/
gen/
out/
target/
dist/
[Dd]ebug/
[Rr]elease/
generated/

############################# Node.js ##############################
node_modules
coverage
.npm
.eslintcache
.grunt

*/package-lock.json

*/node_modules

*/.editorconfig

node_modules/

############################# other ##############################
products/
script/log.txt
script/*.pyc
dist
temp
debugger_app
npm-debug.log*
yarn-debug.log*
yarn-error.log*
/test/unit/coverage/
/test/e2e/reports/
selenium-debug.log
.idea
*.ntvs*
*.njsproj
*.sln
.expo
.env.local
.env.development.local
.env.test.local
.env.production.local

# Coverage
.nycrc.json

.kfc*
.history