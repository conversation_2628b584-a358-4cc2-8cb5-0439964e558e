# 在流水线中，是KNode团队自己编译的node18版本
# 本文件由 @gundam/gundam-plugin-kdev 插件负责更新。

set -euxo pipefail

# source ~/.nvm/nvm.sh
# use_node_version="18"
# nvm use ${use_node_version}

echo "当前 node 版本: $(node -v)"
echo "当前 pnpm 版本: $(pnpm -v)"

pnpm -g add pnpm@8

echo "当前路径是：$(pwd)"

pnpm install

# 获取指定版本
if [ -z ${GUNDAM_PLUGIN_KDEV_VERSION:-} ]; then
  GUNDAM_KDEV_SCRIPTS_VERSION="@latest"
else
  GUNDAM_KDEV_SCRIPTS_VERSION="@$GUNDAM_PLUGIN_KDEV_VERSION"
fi

alias gundam-kdev-scripts="pnpm --package=@gundam/gundam-plugin-kdev$GUNDAM_KDEV_SCRIPTS_VERSION dlx gundam-kdev-scripts"

# git pull --unshallow
gundam-kdev-scripts --version
# lint
gundam-kdev-scripts krn lint
# build
gundam-kdev-scripts krn build

unalias gundam-kdev-scripts
