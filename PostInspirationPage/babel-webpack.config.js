const originConfig = require('./babel.config');

// 在使用 Webpack 打包时替换配置
if (process.env.KRN_BUILD_TYPE === 'WEBPACK') {
  // 开启 disableImportExportTransform
  originConfig.presets = originConfig.presets?.map(preset => {
    if (preset === 'module:metro-react-native-babel-preset') {
      return [preset, { disableImportExportTransform: true }];
    }
    if (Array.isArray(preset) && preset[0] === 'module:metro-react-native-babel-preset') {
      preset[1] = { ...preset[1], disableImportTransform: true };
    }
    return preset;
  });

  // 删除 @social/babel-plugin-import
  const removePluginImport = plugins =>
    plugins.filter(plugin =>
      typeof plugin === 'string'
        ? plugin !== '@social/babel-plugin-import'
        : Array.isArray(plugin)
          ? plugin[0] !== '@social/babel-plugin-import'
          : true,
    );
  if (originConfig.env?.production?.plugins?.length) {
    originConfig.env.production.plugins = removePluginImport(originConfig.env.production.plugins);
  }
  if (originConfig.plugins?.length) {
    originConfig.plugins = removePluginImport(originConfig.plugins);
  }
}

module.exports = originConfig;
