const { createHash } = require('node:crypto');
const { existsSync, readFileSync } = require('node:fs');

const cacheVersion = createHash('md5')
  .update(
    ['./.babel-plugin-macrosrc.yaml', './.gundam/gundam.json']
      .filter(file => existsSync(file))
      .map(file => readFileSync(file))
      .join(''),
  )
  .digest('hex');

module.exports = {
  resolver: {
    sourceExts: ['ts', 'tsx', 'js', 'jsx', 'mjs', 'json', 'cjs'],
  },
  transformer: {
    getTransformOptions: async () => ({
      transform: {
        experimentalImportSupport: true,
        inlineRequires: true,
      },
    }),
  },
  cacheVersion,
};
