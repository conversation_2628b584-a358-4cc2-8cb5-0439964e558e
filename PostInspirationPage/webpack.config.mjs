import { createRequire } from 'module';

const require = createRequire(import.meta.url);
import { defineWebpackConfig } from '@gundam/webpack';

const { InlineRequireWebpackPlugin } = require('@krn/inline-require-webpack-plugin');
const KRNModuleRequireTracePlugin = require('@krn/module-require-trace-webpack-plugin');
const CircularDependencyPlugin = require('circular-dependency-plugin');

const plugins = [
  new CircularDependencyPlugin({
    exclude: /node_modules/,
    failOnError: true, // 出现循环依赖时打包失败
    cwd: new URL('.', import.meta.url).pathname,
  }),
  new InlineRequireWebpackPlugin(), // 开启 metro 中的 inlineRequire
];

if (process.env.KRN_DEV_BUILD_MODE === 'true') {
  // 模块 trace 追踪，目前仅限 debug 下使用
  plugins.push(new KRNModuleRequireTracePlugin());
}

export default defineWebpackConfig({
  plugins,
  optimization: {
    concatenateModules: false, // 禁止合并模块，配合 inlineRequire 使用
  },
  resolve: {
    exportsFields: [],
  },
});
