const { readFileSync } = require('node:fs');

module.exports = {
  root: true,
  env: {
    es6: true,
  },
  extends: [
    'eslint:recommended',
    'plugin:import/recommended',
    'plugin:import/react',
    'plugin:import/react-native',
    'plugin:import/typescript',
    'plugin:@typescript-eslint/recommended',
    'plugin:react/recommended',
    'plugin:react-hooks/recommended',
    'plugin:react-native/all',

    // prettier 相关规则放在最后，用于禁用冲突规则
    'plugin:prettier/recommended',
  ],
  plugins: ['@react-native-community'],
  parser: '@typescript-eslint/parser',
  parserOptions: {
    ecmaFeatures: {
      jsx: true,
    },
    ecmaVersion: '2015',
    sourceType: 'module',
    tsconfigRootDir: __dirname,
  },
  // 使用 ignorePatterns 而不使用 .eslintignore
  // 为什么：https://github.com/eslint/eslint/issues/13798#issuecomment-721525557
  ignorePatterns: readFileSync(require.resolve('./.gitignore', { paths: [__dirname] }), 'utf8')
    .split('\n')
    .map(line => line.trim())
    .filter(line => line && !line.startsWith('#')),
  rules: {
    // 与 prettier 冲突的规则会导致 eslint --fix 时产生错误代码！
    // 添加规则前请确认：规则是否与 prettier 冲突？
    // 可以在添加规则后，使用 eslint-config-prettier 命令进行检查确认

    'prettier/prettier': ['error', require('./.prettierrc.json')],

    'no-constant-condition': ['error', { checkLoops: false }],
    'no-empty': ['error', { allowEmptyCatch: true }],
    'object-shorthand': ['error', 'always', { avoidExplicitReturnArrows: true }],
    quotes: ['error', 'single', { avoidEscape: true, allowTemplateLiterals: false }],
    'sort-imports': ['warn', { ignoreDeclarationSort: true, allowSeparatedGroups: true }],

    'no-restricted-imports': [
      'error',
      {
        paths: [
          {
            name: 'react-native',
            importNames: ['Image'],
            message:
              '请使用 @kds/image 提供的 KwaiImage 代替，以支持 WebP 且避免 iOS15 以上系统崩溃',
          },
        ],
      },
    ],
    'no-restricted-syntax': [
      'warn',
      {
        selector: 'TSEnumDeclaration:not([const=true])',
        message: '建议使用 const enum 常量枚举，搭配 babel optimizeConstEnums 参数可以缩减代码体积',
      },
    ],

    'import/newline-after-import': ['warn', { considerComments: true }],
    'import/no-duplicates': ['warn', { 'prefer-inline': true }],
    'import/no-extraneous-dependencies': [
      'warn',
      {
        devDependencies: false,
        optionalDependencies: false,
        peerDependencies: true,
        bundledDependencies: false,
      },
    ],
    'import/no-namespace': 'warn',
    'import/no-useless-path-segments': ['warn', { noUselessIndex: true }],
    'import/order': [
      'warn',
      {
        groups: ['builtin', 'external', 'internal', 'parent', 'sibling', 'index', 'object'],
        pathGroups: [
          { pattern: 'react', group: 'builtin', position: 'before' },
          {
            pattern: 'react-native',
            group: 'builtin',
            position: 'after',
          },
        ],
        pathGroupsExcludedImportTypes: ['react', 'react-native'],
        alphabetize: { order: 'asc' },
      },
    ],

    '@typescript-eslint/consistent-type-imports': ['warn', { fixStyle: 'inline-type-imports' }],
    '@typescript-eslint/no-empty-function': 'off',
    '@typescript-eslint/no-empty-object-type': ['error', { allowInterfaces: 'always' }],
    '@typescript-eslint/no-require-imports': 'off',
    '@typescript-eslint/no-shadow': ['error', { ignoreOnInitialization: true }],
    '@typescript-eslint/no-unused-expressions': [
      'error',
      {
        allowShortCircuit: true,
        allowTernary: true,
        allowTaggedTemplates: true,
        enforceForJSX: true,
      },
    ],
    '@typescript-eslint/no-use-before-define': 'warn',

    '@typescript-eslint/no-explicit-any': 'off',

    'react/display-name': 'off',
    'react/jsx-filename-extension': ['warn', { extensions: ['.tsx', '.jsx'] }],
    'react/prop-types': 'off',

    'react-hooks/exhaustive-deps': 'error',

    '@react-native-community/platform-colors': 'error',

    'react-native/no-unused-styles': 'off',
    'react-native/no-inline-styles': 'warn',
    'react-native/no-color-literals': 'off',
    'react-native/sort-styles': 'warn',
    'react-native/split-platform-components': 'error',
    'react-native/no-raw-text': ['warn', { skip: ['Text.Text'] }],
    'react-native/no-single-element-style-arrays': 'warn',
  },
  settings: {
    react: { version: '16.11' },
    'import/internal-regex': '^@/',
    'import/resolver': {
      typescript: {
        project: __dirname,
      },
    },
    'react-native/style-sheet-object-names': ['PlusStyle', 'StyleSheet'],
  },
  overrides: [
    {
      files: ['*.js', '*.cjs', '*.mjs'],
      env: {
        commonjs: true,
        node: true,
      },
      extends: ['eslint:recommended', 'plugin:import/recommended', 'plugin:prettier/recommended'],
      parser: '@typescript-eslint/parser',
      parserOptions: {
        ecmaVersion: 'latest',
        sourceType: 'module',
      },
      rules: {
        'import/no-extraneous-dependencies': 'off',
      },
    },
  ],
};
