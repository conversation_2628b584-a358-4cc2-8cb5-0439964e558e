import React, { memo } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import { type ItemData } from '@lux/krn-waterfall-list-view/lib/typescript/type';

const styles = StyleSheet.create({
  container: {
    alignItems: 'center',
    justifyContent: 'center',
  },
  itemContainer: {
    alignItems: 'center',
    borderRadius: 10,
    height: '100%',
    justifyContent: 'center',
    width: '95%',
  },
  itemText: {
    color: 'white',
    fontSize: 24,
  },
});

const Item = ({ data }: { data: ItemData }) => {
  const { itemData, columnIndex } = data;

  const align: Record<number, 'flex-start' | 'center' | 'flex-end'> = {
    0: 'flex-start',
    1: 'center',
    2: 'flex-end',
  };

  return (
    <View
      style={[
        styles.container,
        {
          height: itemData.h,
          alignItems: align[columnIndex],
        },
      ]}
    >
      <View
        style={[
          styles.itemContainer,
          {
            backgroundColor: itemData.bg,
          },
        ]}
      >
        <Text style={styles.itemText}>{itemData.name}</Text>
      </View>
    </View>
  );
};
export default memo<typeof Item>(Item);
