import React, { memo, useEffect, useRef, useState } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import WaterFallList from '@lux/krn-waterfall-list-view';
import { type IWaterFallList } from '@lux/krn-waterfall-list-view/lib/typescript/type';
import Item from './item';

// 定义数据项类型
interface ListItem {
  h: number;
  bg: string;
  index: number;
  key: number;
  name: number;
}

const colors = [
  '#5A479A',
  '#001694',
  '#32296B',
  '#D1D1D1',
  '#641024',
  '#FE3666',
  '#292647',
  '#B0E38F',
  '#6195FC',
  '#444444',
  '#FFD283',
  '#52210D',
  '#FFE8ED',
  '#3C325F',
  '#19191E',
];

let index = 0;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FAB5B5',
    height: '100%',
    paddingHorizontal: 5,
    width: '100%',
  },
  contentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    alignItems: 'center',
    backgroundColor: 'pink',
    height: '100%',
    justifyContent: 'center',
    width: '100%',
  },
  emptyText: {
    color: 'red',
    fontSize: 30,
  },
  footerContainer: {
    alignItems: 'center',
    backgroundColor: 'pink',
    height: 50,
    justifyContent: 'center',
    width: '100%',
  },
  footerText: {
    color: 'red',
    fontSize: 30,
  },
  headerComponent: {
    backgroundColor: 'orange',
    height: 300,
    marginBottom: 20,
    width: '100%',
  },
  refreshButton: {
    alignItems: 'center',
    backgroundColor: 'blue',
    height: 50,
    justifyContent: 'center',
    marginTop: 20,
    width: '100%',
  },
  refreshButtonText: {
    color: 'white',
  },
  separator: {
    height: 10,
    width: '100%',
  },
});

const getList = (length = 15): ListItem[] => {
  return Array.from({ length }, () => {
    index++;
    return {
      h: Math.floor(Math.random() * 80) + 100,
      bg: colors[Math.floor(Math.random() * colors.length)],
      index,
      key: index,
      name: index,
    };
  });
};

const App = () => {
  const [list, changeList] = useState<ListItem[]>([]);
  const waterfallRef = useRef<IWaterFallList>(null);

  const refresh = () => {
    index = 0;
    waterfallRef.current?.refreshList();
    changeList(getList(20));

    console.log('test scrollToOffset', waterfallRef.current?.flatList?.scrollToOffset);
  };

  const onEndReached = () => {
    const nList = [...list, ...getList(20)];
    console.log('test onEndReached', nList);
    changeList(nList);
  };

  const onScroll = () => {
    console.log('test onScroll');
  };

  useEffect(() => {
    changeList(getList(20));
  }, []);

  return (
    <View style={styles.container}>
      <TouchableOpacity onPress={refresh}>
        <View style={styles.refreshButton}>
          <Text style={styles.refreshButtonText}>刷新列表</Text>
        </View>
      </TouchableOpacity>
      <WaterFallList
        keyExtractor={(_item, key_index) => `row_${key_index}`}
        ref={waterfallRef}
        ItemSeparatorComponent={() => {
          return <View style={styles.separator}></View>;
        }}
        initialNumToRender={10}
        windowSize={10}
        ListHeaderComponent={() => {
          return <View style={styles.headerComponent}></View>;
        }}
        onScroll={onScroll}
        renderItem={({ item }) => <Item key={item.index} data={item}></Item>}
        data={list}
        contentContainerStyle={styles.contentContainer}
        onEndReachedThreshold={0.5}
        onEndReached={onEndReached}
        numColumns={2}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => {
          return (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>empty</Text>
            </View>
          );
        }}
        ListFooterComponent={() => {
          return (
            <View style={styles.footerContainer}>
              <Text style={styles.footerText}>正在加载中</Text>
            </View>
          );
        }}
      />
    </View>
  );
};

export default memo<typeof App>(App);
