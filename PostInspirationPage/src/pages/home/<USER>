import React, { type VFC, memo } from 'react';
import { Button, Platform, Text, View } from 'react-native';
import { WrapContext } from '@kid-ui/krn';
import { type PageProps, usePage, useWebLogger } from '@mfe/plus';
import { PageList } from '@/constants';
import { type Pages } from '@/router';
import { GuideDialog } from '../dialog/GuideDialog';
import { useStyles } from './styles';

// 检测是否为 Web 平台
const isWeb = Platform.OS === 'web';

const App: VFC<PageProps<Pages, PageList.Index>> = ({ navigation }) => {
  const { data, loading, hasError, refresh } = usePage<Pages, PageList.Index>();
  const styles = useStyles();

  const weblogger = useWebLogger();

  // 显示引导弹窗的函数
  const showGuideDialog = () => {
    console.log('🔥 显示引导弹窗');
    weblogger.collect('CLICK', {
      action: 'button_click',
      params: { button: 'show_guide_dialog' },
    });
    GuideDialog.show({
      onClose() {
        console.log('✅ 引导弹窗被关闭了');
        weblogger.collect('CLICK', {
          action: 'guide_dialog_close',
          params: { trigger: 'manual_show' },
        });
      },
    });
  };

  return (
    <View style={styles.container}>
      <Text>{loading ? '正在加载页面数据' : '页面数据已加载'}</Text>
      {hasError ? (
        <Text>数据加载失败</Text>
      ) : (
        <Text>渲染数据：{JSON.stringify(data, null, 2)}</Text>
      )}
      <Button
        title="重新请求数据"
        onPress={() => {
          weblogger.collect('CLICK', {
            action: 'button_click',
            params: { button: 'refresh' },
          });
          refresh();
        }}
      />
      <Button
        title="跳转到 Page 2"
        onPress={() => {
          weblogger.collect('CLICK', {
            action: 'button_click',
            params: { button: 'navigate_page_2' },
          });
          navigation.navigate(PageList.Page2);
        }}
      />
      <Button title="显示引导弹窗" onPress={showGuideDialog} />
    </View>
  );
};
export default WrapContext(memo<typeof App>(App), {
  styleConfig: {
    viewportWidthProvider: {
      getViewportWidth: ({ width }) => (isWeb ? 414 : Math.min(width, 414)),
    },
  },
});
