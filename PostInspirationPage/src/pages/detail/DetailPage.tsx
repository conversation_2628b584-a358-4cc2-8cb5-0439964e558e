import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Animated, Easing, StyleSheet, Text, View } from 'react-native';
import WaterFallList from '@lux/krn-waterfall-list-view';
import {
  type IWaterFallList,
  type ItemData,
} from '@lux/krn-waterfall-list-view/lib/typescript/type';
import { type PageProps, useWebLogger } from '@mfe/plus';
import { PageList } from '@/constants';
import { type Pages } from '@/router';

import DetailArticleCard from './DetailArticleCard';
import { type ArticleBean, type DetailModel } from './DetailModels';
import DetailPageHeader from './DetailPageHeader';
import DetailTitleBarView from './DetailTitleBarView';
import { DetailUtils } from './DetailUtils';

interface DetailPageProps {
  model: DetailModel;
  navigation: PageProps<Pages, PageList.Page2>['navigation'];
}

// 使用ArticleBean作为数据类型，通过ItemData包装

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FAB5B5',
    height: '100%',
    paddingHorizontal: 5,
    width: '100%',
  },
  contentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    alignItems: 'center',
    backgroundColor: 'pink',
    height: '100%',
    justifyContent: 'center',
    width: '100%',
  },
  emptyText: {
    color: 'red',
    fontSize: 30,
  },
  footerContainer: {
    alignItems: 'center',
    backgroundColor: 'pink',
    height: 50,
    justifyContent: 'center',
    width: '100%',
  },
  footerText: {
    color: 'red',
    fontSize: 30,
  },
  headerComponent: {
    backgroundColor: 'orange',
    height: 300,
    marginBottom: 20,
    width: '100%',
  },
  refreshButton: {
    alignItems: 'center',
    backgroundColor: 'blue',
    height: 50,
    justifyContent: 'center',
    marginTop: 20,
    width: '100%',
  },
  refreshButtonText: {
    color: 'white',
  },
  separator: {
    height: 10,
    width: '100%',
  },
});

const getArticles = (length = 15): ArticleBean[] => {
  const baseArticles = DetailUtils.createArticles();
  const result: ArticleBean[] = [];

  for (let i = 0; i < length; i++) {
    const baseArticle = baseArticles[i % baseArticles.length];
    result.push({
      ...baseArticle,
      id: `${baseArticle.id}_${i}`,
      userName: `${baseArticle.userName}_${i}`,
      likeCount: Math.floor(Math.random() * 1000) + 1,
    });
  }

  return result;
};

// 将ArticleBean转换为ItemData格式
const convertToItemData = (articles: ArticleBean[]): ItemData[] => {
  return articles.map((article, index) => ({
    index,
    offsetTop: 0,
    itemH: Math.floor(Math.random() * 100) + 200, // 随机高度
    itemData: article,
    columnIndex: index % 2, // 双列布局
  }));
};

const DetailPage: React.FC<DetailPageProps> = ({ model, navigation }) => {
  const weblogger = useWebLogger();

  // 滚动检测相关状态
  const actionAreaRef = useRef<View>(null);
  const [actionAreaLayout, setActionAreaLayout] = useState({ y: 0, height: 0 });
  const [isActionAreaVisible, setIsActionAreaVisible] = useState(true);

  // 底部操作区动画状态
  const bottomActionAreaTranslateY = useRef(new Animated.Value(89)).current;

  // 监听操作功能区可见性变化，控制底部操作区动画
  useEffect(() => {
    if (isActionAreaVisible) {
      // 操作区可见时，隐藏底部操作区（下降动画）
      Animated.timing(bottomActionAreaTranslateY, {
        toValue: 89,
        duration: 200,
        easing: Easing.bezier(0.32, 0.94, 0.6, 1), // cubicOut曲线
        useNativeDriver: true,
      }).start();
    } else {
      // 操作区不可见时，显示底部操作区（上推动画）
      Animated.timing(bottomActionAreaTranslateY, {
        toValue: 0,
        duration: 200,
        easing: Easing.bezier(0.32, 0.94, 0.6, 1), // cubicOut曲线
        useNativeDriver: true,
      }).start();
    }
  }, [isActionAreaVisible, bottomActionAreaTranslateY]);

  const handleBackPress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'back_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate(PageList.Index);
    }
  }, [navigation, weblogger, model.materialType]);

  const handleHomePress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'home_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    console.log('进入灵感库按钮被点击了');
  }, [weblogger, model.materialType]);

  // 处理操作功能区布局变化
  const handleActionAreaLayout = useCallback((event: any) => {
    const { y, height } = event.nativeEvent.layout;
    setActionAreaLayout({ y, height });
  }, []);

  const [list, changeList] = useState<ItemData[]>([]);
  const waterfallRef = useRef<IWaterFallList>(null);

  const refresh = () => {
    waterfallRef.current?.refreshList();
    const articles = getArticles(20);
    changeList(convertToItemData(articles));

    console.log('test scrollToOffset', waterfallRef.current?.flatList?.scrollToOffset);
  };

  const onEndReached = useCallback(() => {
    changeList(prevList => {
      const articles = getArticles(20);
      const newItems = convertToItemData(articles);
      const nList = [...prevList, ...newItems];
      console.log('test onEndReached', nList);
      return nList;
    });
  }, []);

  const onScroll = useCallback(() => {
    console.log('test onScroll');
  }, []);

  // 渲染组件函数
  const renderSeparator = useCallback(() => {
    return <View style={styles.separator}></View>;
  }, []);

  const renderHeader = useCallback(() => {
    return (
      <DetailPageHeader
        ref={actionAreaRef}
        model={model}
        onActionAreaLayout={handleActionAreaLayout}
      />
    );
  }, [model, handleActionAreaLayout]);

  const renderItem = useCallback(({ item }: { item: ItemData }) => {
    const article = item.itemData as ArticleBean;
    return <DetailArticleCard key={article.id} article={article} />;
  }, []);

  const renderEmpty = useCallback(() => {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>empty</Text>
      </View>
    );
  }, []);

  const renderFooter = useCallback(() => {
    return (
      <View style={styles.footerContainer}>
        <Text style={styles.footerText}>正在加载中</Text>
      </View>
    );
  }, []);

  const keyExtractor = useCallback((_item: any, key_index: number) => `row_${key_index}`, []);

  useEffect(() => {
    const articles = getArticles(20);
    changeList(convertToItemData(articles));
  }, []);

  return (
    <View style={styles.container}>
      {/* 标题栏 */}
      <DetailTitleBarView
        model={model}
        onBackPress={handleBackPress}
        onHomePress={handleHomePress}
      />

      <WaterFallList
        keyExtractor={keyExtractor}
        ref={waterfallRef}
        ItemSeparatorComponent={renderSeparator}
        initialNumToRender={10}
        windowSize={10}
        ListHeaderComponent={renderHeader}
        onScroll={onScroll}
        renderItem={renderItem}
        data={list}
        contentContainerStyle={styles.contentContainer}
        onEndReachedThreshold={0.5}
        onEndReached={onEndReached}
        numColumns={2}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmpty}
        ListFooterComponent={renderFooter}
      />
    </View>
  );
};

export default DetailPage;
