import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Animated, Easing, StyleSheet, Text, View } from 'react-native';
import WaterFallList from '@lux/krn-waterfall-list-view';
import { type IWaterFallList } from '@lux/krn-waterfall-list-view/lib/typescript/type';
import { type PageProps, useWebLogger } from '@mfe/plus';
import { PageList } from '@/constants';
import { type Pages } from '@/router';
import Item from '../waterfall/item';
import { type DetailModel } from './DetailModels';
import DetailPageHeader from './DetailPageHeader';
import DetailTitleBarView from './DetailTitleBarView';

interface DetailPageProps {
  model: DetailModel;
  navigation: PageProps<Pages, PageList.Page2>['navigation'];
}

// 定义数据项类型
interface ListItem {
  h: number;
  bg: string;
  index: number;
  key: number;
  name: number;
}

const colors = [
  '#5A479A',
  '#001694',
  '#32296B',
  '#D1D1D1',
  '#641024',
  '#FE3666',
  '#292647',
  '#B0E38F',
  '#6195FC',
  '#444444',
  '#FFD283',
  '#52210D',
  '#FFE8ED',
  '#3C325F',
  '#19191E',
];

let index = 0;

const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FAB5B5',
    height: '100%',
    paddingHorizontal: 5,
    width: '100%',
  },
  contentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    alignItems: 'center',
    backgroundColor: 'pink',
    height: '100%',
    justifyContent: 'center',
    width: '100%',
  },
  emptyText: {
    color: 'red',
    fontSize: 30,
  },
  footerContainer: {
    alignItems: 'center',
    backgroundColor: 'pink',
    height: 50,
    justifyContent: 'center',
    width: '100%',
  },
  footerText: {
    color: 'red',
    fontSize: 30,
  },
  headerComponent: {
    backgroundColor: 'orange',
    height: 300,
    marginBottom: 20,
    width: '100%',
  },
  refreshButton: {
    alignItems: 'center',
    backgroundColor: 'blue',
    height: 50,
    justifyContent: 'center',
    marginTop: 20,
    width: '100%',
  },
  refreshButtonText: {
    color: 'white',
  },
  separator: {
    height: 10,
    width: '100%',
  },
});

const getList = (length = 15): ListItem[] => {
  return Array.from({ length }, () => {
    index++;
    return {
      h: Math.floor(Math.random() * 80) + 100,
      bg: colors[Math.floor(Math.random() * colors.length)],
      index,
      key: index,
      name: index,
    };
  });
};

const DetailPage: React.FC<DetailPageProps> = ({ model, navigation }) => {
  const weblogger = useWebLogger();

  // 滚动检测相关状态
  const actionAreaRef = useRef<View>(null);
  const [actionAreaLayout, setActionAreaLayout] = useState({ y: 0, height: 0 });
  const [isActionAreaVisible, setIsActionAreaVisible] = useState(true);

  // 底部操作区动画状态
  const bottomActionAreaTranslateY = useRef(new Animated.Value(89)).current;

  // 监听操作功能区可见性变化，控制底部操作区动画
  useEffect(() => {
    if (isActionAreaVisible) {
      // 操作区可见时，隐藏底部操作区（下降动画）
      Animated.timing(bottomActionAreaTranslateY, {
        toValue: 89,
        duration: 200,
        easing: Easing.bezier(0.32, 0.94, 0.6, 1), // cubicOut曲线
        useNativeDriver: true,
      }).start();
    } else {
      // 操作区不可见时，显示底部操作区（上推动画）
      Animated.timing(bottomActionAreaTranslateY, {
        toValue: 0,
        duration: 200,
        easing: Easing.bezier(0.32, 0.94, 0.6, 1), // cubicOut曲线
        useNativeDriver: true,
      }).start();
    }
  }, [isActionAreaVisible, bottomActionAreaTranslateY]);

  const handleBackPress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'back_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate(PageList.Index);
    }
  }, [navigation, weblogger, model.materialType]);

  const handleHomePress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'home_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    console.log('进入灵感库按钮被点击了');
  }, [weblogger, model.materialType]);

  // 处理操作功能区布局变化
  const handleActionAreaLayout = useCallback((event: any) => {
    const { y, height } = event.nativeEvent.layout;
    setActionAreaLayout({ y, height });
  }, []);

  const [list, changeList] = useState<ListItem[]>([]);
  const waterfallRef = useRef<IWaterFallList>(null);

  const refresh = () => {
    index = 0;
    waterfallRef.current?.refreshList();
    changeList(getList(20));

    console.log('test scrollToOffset', waterfallRef.current?.flatList?.scrollToOffset);
  };

  const onEndReached = useCallback(() => {
    changeList(prevList => {
      const nList = [...prevList, ...getList(20)];
      console.log('test onEndReached', nList);
      return nList;
    });
  }, []);

  const onScroll = useCallback(() => {
    console.log('test onScroll');
  }, []);

  // 渲染组件函数
  const renderSeparator = useCallback(() => {
    return <View style={styles.separator}></View>;
  }, []);

  const renderHeader = useCallback(() => {
    return (
      <DetailPageHeader
        ref={actionAreaRef}
        model={model}
        onActionAreaLayout={handleActionAreaLayout}
      />
    );
  }, [model, handleActionAreaLayout]);

  const renderItem = useCallback(({ item }: { item: any }) => {
    return <Item key={item.index} data={item}></Item>;
  }, []);

  const renderEmpty = useCallback(() => {
    return (
      <View style={styles.emptyContainer}>
        <Text style={styles.emptyText}>empty</Text>
      </View>
    );
  }, []);

  const renderFooter = useCallback(() => {
    return (
      <View style={styles.footerContainer}>
        <Text style={styles.footerText}>正在加载中</Text>
      </View>
    );
  }, []);

  const keyExtractor = useCallback((_item: any, key_index: number) => `row_${key_index}`, []);

  useEffect(() => {
    changeList(getList(20));
  }, []);

  return (
    <View style={styles.container}>
      {/* 标题栏 */}
      <DetailTitleBarView
        model={model}
        onBackPress={handleBackPress}
        onHomePress={handleHomePress}
      />

      <WaterFallList
        keyExtractor={keyExtractor}
        ref={waterfallRef}
        ItemSeparatorComponent={renderSeparator}
        initialNumToRender={10}
        windowSize={10}
        ListHeaderComponent={renderHeader}
        onScroll={onScroll}
        renderItem={renderItem}
        data={list}
        contentContainerStyle={styles.contentContainer}
        onEndReachedThreshold={0.5}
        onEndReached={onEndReached}
        numColumns={2}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={renderEmpty}
        ListFooterComponent={renderFooter}
      />
    </View>
  );
};

export default DetailPage;
