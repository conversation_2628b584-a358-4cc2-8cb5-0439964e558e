import React, { useCallback, useEffect, useRef, useState } from 'react';
import { Animated, Easing, ScrollView, StyleSheet, View, Text } from 'react-native';
import { type PageProps, useWebLogger } from '@mfe/plus';
import { PageList } from '@/constants';
import { type Pages } from '@/router';
import DetailActionView from './DetailActionView';
import { type DetailModel } from './DetailModels';
import DetailPageHeader from './DetailPageHeader';
import DetailTitleBarView from './DetailTitleBarView';
import WaterFallList from '@lux/krn-waterfall-list-view';
import { type IWaterFallList } from '@lux/krn-waterfall-list-view/lib/typescript/type';
import Item from './../demo/item';

interface DetailPageProps {
  model: DetailModel;
  navigation: PageProps<Pages, PageList.Page2>['navigation'];
}


// 定义数据项类型
interface ListItem {
  h: number;
  bg: string;
  index: number;
  key: number;
  name: number;
}


const styles = StyleSheet.create({
  container: {
    backgroundColor: '#FAB5B5',
    height: '100%',
    paddingHorizontal: 5,
    width: '100%',
  },
  contentContainer: {
    flexGrow: 1,
  },
  emptyContainer: {
    alignItems: 'center',
    backgroundColor: 'pink',
    height: '100%',
    justifyContent: 'center',
    width: '100%',
  },
  emptyText: {
    color: 'red',
    fontSize: 30,
  },
  footerContainer: {
    alignItems: 'center',
    backgroundColor: 'pink',
    height: 50,
    justifyContent: 'center',
    width: '100%',
  },
  footerText: {
    color: 'red',
    fontSize: 30,
  },
  headerComponent: {
    backgroundColor: 'orange',
    height: 300,
    marginBottom: 20,
    width: '100%',
  },
  refreshButton: {
    alignItems: 'center',
    backgroundColor: 'blue',
    height: 50,
    justifyContent: 'center',
    marginTop: 20,
    width: '100%',
  },
  refreshButtonText: {
    color: 'white',
  },
  separator: {
    height: 10,
    width: '100%',
  },
});


const DetailPage: React.FC<DetailPageProps> = ({ model, navigation }) => {
  const weblogger = useWebLogger();

  // 滚动检测相关状态
  const scrollViewRef = useRef<ScrollView>(null);
  const actionAreaRef = useRef<View>(null);
  const [actionAreaLayout, setActionAreaLayout] = useState({ y: 0, height: 0 });
  const [scrollViewHeight, setScrollViewHeight] = useState(0);
  const [isActionAreaVisible, setIsActionAreaVisible] = useState(true);

  // 底部操作区动画状态
  const bottomActionAreaTranslateY = useRef(new Animated.Value(89)).current;

  // 动画控制函数
  const showBottomActionArea = useCallback(() => {
    Animated.timing(bottomActionAreaTranslateY, {
      toValue: 0,
      duration: 200,
      easing: Easing.bezier(0.32, 0.94, 0.6, 1), // cubicOut曲线
      useNativeDriver: true,
    }).start();
  }, [bottomActionAreaTranslateY]);

  const hideBottomActionArea = useCallback(() => {
    Animated.timing(bottomActionAreaTranslateY, {
      toValue: 89,
      duration: 200,
      easing: Easing.bezier(0.32, 0.94, 0.6, 1), // cubicOut曲线
      useNativeDriver: true,
    }).start();
  }, [bottomActionAreaTranslateY]);

  // 监听操作功能区可见性变化，控制底部操作区动画
  useEffect(() => {
    if (isActionAreaVisible) {
      hideBottomActionArea(); // 操作区可见时，隐藏底部操作区（下降动画）
    } else {
      showBottomActionArea(); // 操作区不可见时，显示底部操作区（上推动画）
    }
  }, [isActionAreaVisible, showBottomActionArea, hideBottomActionArea]);

  const handleBackPress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'back_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    if (navigation.canGoBack()) {
      navigation.goBack();
    } else {
      navigation.navigate(PageList.Index);
    }
  }, [navigation, weblogger, model.materialType]);

  const handleHomePress = useCallback(() => {
    weblogger.collect('CLICK', {
      action: 'home_press',
      params: { from: 'DetailPage', style: model.materialType },
    });
    console.log('进入灵感库按钮被点击了');
  }, [weblogger, model.materialType]);

  // 处理操作功能区布局变化
  const handleActionAreaLayout = useCallback((event: any) => {
    const { y, height } = event.nativeEvent.layout;
    setActionAreaLayout({ y, height });
  }, []);

  // 处理ScrollView布局变化
  const handleScrollViewLayout = useCallback((event: any) => {
    const { height } = event.nativeEvent.layout;
    setScrollViewHeight(height);
  }, []);

  // 处理滚动事件
  const handleScroll = useCallback(
    (event: any) => {
      const scrollY = event.nativeEvent.contentOffset.y;
      const actionAreaTop = actionAreaLayout.y;
      const actionAreaBottom = actionAreaLayout.y + actionAreaLayout.height;
      const viewportBottom = scrollY + scrollViewHeight;

      // 检查操作功能区是否在可见区域内
      const isVisible = actionAreaTop < viewportBottom && actionAreaBottom > scrollY;

      // 如果可见性状态发生变化，输出日志
      if (isVisible !== isActionAreaVisible) {
        setIsActionAreaVisible(isVisible);
        if (!isVisible) {
          console.log('操作功能区已划出可见区域', {
            scrollY,
            actionAreaTop,
            actionAreaBottom,
            viewportBottom,
            materialType: model.materialType,
            materialId: model.materialId,
          });
        } else {
          console.log('操作功能区重新进入可见区域', {
            scrollY,
            actionAreaTop,
            actionAreaBottom,
            viewportBottom,
            materialType: model.materialType,
            materialId: model.materialId,
          });
        }
      }
    },
    [actionAreaLayout, scrollViewHeight, isActionAreaVisible, model.materialType, model.materialId],
  );

  const [list, changeList] = useState<ListItem[]>([]);
    const waterfallRef = useRef<IWaterFallList>(null);
  
    const refresh = () => {
      index = 0;
      waterfallRef.current?.refreshList();
      changeList(getList(20));
  
      console.log('test scrollToOffset', waterfallRef.current?.flatList?.scrollToOffset);
    };
  
    const onEndReached = () => {
      const nList = [...list, ...getList(20)];
      console.log('test onEndReached', nList);
      changeList(nList);
    };
  
    const onScroll = () => {
      console.log('test onScroll');
    };
  
    useEffect(() => {
      changeList(getList(20));
    }, []);
  

  return (
    <View style={styles.container}>
      {/* 标题栏 */}
      <DetailTitleBarView
        model={model}
        onBackPress={handleBackPress}
        onHomePress={handleHomePress}
      />

      <WaterFallList
        keyExtractor={(_item, key_index) => `row_${key_index}`}
        ref={waterfallRef}
        ItemSeparatorComponent={() => {
          return <View style={styles.separator}></View>;
        }}
        initialNumToRender={10}
        windowSize={10}
        ListHeaderComponent={() => {
          // return <View style={styles.headerComponent}></View>;
          
        }}
        onScroll={onScroll}
        renderItem={({ item }) => <Item key={item.index} data={item}></Item>}
        data={list}
        contentContainerStyle={styles.contentContainer}
        onEndReachedThreshold={0.5}
        onEndReached={onEndReached}
        numColumns={2}
        showsVerticalScrollIndicator={false}
        ListEmptyComponent={() => {
          return (
            <View style={styles.emptyContainer}>
              <Text style={styles.emptyText}>empty</Text>
            </View>
          );
        }}
        ListFooterComponent={() => {
          return (
            <View style={styles.footerContainer}>
              <Text style={styles.footerText}>正在加载中</Text>
            </View>
          );
        }}
      />

      {/* 滑动区域 */}
      <ScrollView
        ref={scrollViewRef}
        style={styles.scrollContent}
        showsVerticalScrollIndicator={false}
        onScroll={handleScroll}
        onLayout={handleScrollViewLayout}
        scrollEventThrottle={16}
      >
        {/* 详情页头部内容 */}
        
      </ScrollView>

    </View>
  );
};

export default DetailPage;
