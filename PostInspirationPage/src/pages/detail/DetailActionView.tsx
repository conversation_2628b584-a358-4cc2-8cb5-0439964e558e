import React from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import KwaiImage from '@kds/image';
import { type DetailModel } from './DetailModels';

interface DetailActionViewProps {
  model: DetailModel;
  isSplitEqually?: boolean; // 是否平分布局，true为平分，false为收藏按钮固定宽度100
}

const styles = StyleSheet.create({
  actionContainer: {
    alignItems: 'center',
    backgroundColor: '#FFFFFF',
    flexDirection: 'row',
    height: 56,
    paddingHorizontal: 19,
    width: '100%',
  },
  favoriteButton: {
    alignItems: 'center',
    borderColor: '#eaeaea',
    borderRadius: 20,
    borderWidth: 1,
    flexDirection: 'row',
    height: 40,
    justifyContent: 'center',
    marginRight: 4,
  },
  favoriteIcon: {
    height: 20,
    marginRight: 4,
    width: 20,
  },
  favoriteText: {
    color: '#222222',
    fontSize: 14,
  },
  useButton: {
    alignItems: 'center',
    backgroundColor: '#FE3666',
    borderRadius: 20,
    flexDirection: 'row',
    height: 40,
    justifyContent: 'center',
    marginLeft: 4,
  },
  useButtonText: {
    color: '#ffffff',
    fontSize: 14,
    fontWeight: '500',
  },
  useIcon: {
    height: 20,
    marginRight: 4,
    width: 20,
  },
});

const DetailActionView: React.FC<DetailActionViewProps> = ({ model, isSplitEqually = false }) => {
  // 根据materialType确定按钮文本和图标
  const useButtonText = model.materialType === 'magic' ? '使用魔法' : '使用模板';
  const useButtonIcon = require('../../imgs/icon_record.png');

  // 事件处理函数
  const handleFavoritePress = () => {
    console.log('收藏按钮被点击了，当前样式:', model.materialType, '详情ID:', model.materialId);
  };

  const handleUsePress = () => {
    console.log('使用按钮被点击了，当前样式:', model.materialType, '详情ID:', model.materialId);
  };
  const favoriteButtonStyle = isSplitEqually
    ? {
        ...styles.favoriteButton,
        flex: 1, // 平分时占1份
      }
    : {
        ...styles.favoriteButton,
        width: 100, // 固定宽度100
      };

  const useButtonStyle = isSplitEqually
    ? {
        ...styles.useButton,
        flex: 1, // 平分时占1份
      }
    : {
        ...styles.useButton,
        flex: 1, // 固定收藏按钮宽度时，使用按钮占据剩余空间
      };

  return (
    <View style={styles.actionContainer}>
      {/* 收藏按钮 */}
      <TouchableOpacity style={favoriteButtonStyle} onPress={handleFavoritePress}>
        <KwaiImage
          source={require('../../imgs/icon_favoriate.png')}
          style={styles.favoriteIcon}
          resizeMode="contain"
        />
        <Text style={styles.favoriteText}>收藏</Text>
      </TouchableOpacity>

      {/* 使用按钮 */}
      <TouchableOpacity style={useButtonStyle} onPress={handleUsePress}>
        <KwaiImage source={useButtonIcon} style={styles.useIcon} resizeMode="contain" />
        <Text style={styles.useButtonText}>{useButtonText}</Text>
      </TouchableOpacity>
    </View>
  );
};

export default DetailActionView;
