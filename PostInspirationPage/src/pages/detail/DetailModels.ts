/**
 * 详情页数据模型
 */
export interface DetailModel {
  /** 素材类型 */
  materialType: string;
  /** 素材ID */
  materialId: string;
  /** 素材名称 */
  materialName: string;
  /** 封面图片源 */
  coverSource: ImageSource;
  /** 封面图片宽 */
  coverWidth: number;
  /** 封面图片高 */
  coverHeight: number;
  /** 视频源 */
  videoSource: VideoSource;
  /** 视频宽 */
  videoWidth: number;
  /** 视频高 */
  videoHeight: number;
  /**
   * 使用描述
   * 例如：10万次使用 • 5个图片 • 时长00:15
   */
  usageDescription: string;
  /** 用户名 */
  userName: string;
  /** 用户头像源 */
  userAvatarSource: ImageSource;
  /** 跳转链接 */
  scheme: string;
  /** 是否收藏状态 */
  isFavorite: boolean;
  /** 进入详情页的来源 */
  enterFrom: string;
}

/**
 * ArticleBean - 单个文章的数据结构
 */
export interface ArticleBean {
  /** 文章ID */
  id: string;
  /** 文章类型 视频/单图/多图 */
  type: string;
  /** 封面图片源 */
  coverSource: any;
  /** 封面图片宽 */
  coverWidth: number;
  /** 封面图片高 */
  coverHeight: number;
  /** 视频时长 */
  videoDuration: number;
  /** 文章内容 */
  content: string;
  /** 用户名 */
  userName: string;
  /** 用户头像源 */
  userAvatarSource: ImageSource;
  /** 点赞数 */
  likeCount: number;
  /** 发布时间 1天前/2天前/04-05 */
  postTime: string;
  /** 跳转链接 */
  scheme: string;
}

export declare type ImageSource = {
  uri?: string;
  uris?: {
    cdn: string;
    url: string;
  }[];
};

export declare type VideoSource = {
  uri?: string;
  uris?: Array<string>;
  isManifest?: boolean;
  isHlsMaster?: boolean;
  shouldCache?: boolean;
  isNetwork?: boolean;
  alphaType?: number;
  isAsset?: boolean;
  isRawResource?: boolean;
  type?: string;
};
