import React, { forwardRef } from 'react';
import { StyleSheet, Text, View } from 'react-native';
import DetailActionView from './DetailActionView';
import DetailArticleWaterfallView from './DetailArticleWaterfallView';
import DetailCard from './DetailCard';
import { type DetailModel } from './DetailModels';

interface DetailPageHeaderProps {
  model: DetailModel;
  onActionAreaLayout?: (event: any) => void;
}

const styles = StyleSheet.create({
  actionArea: {
    marginTop: 6,
  },
  container: {
    backgroundColor: '#fff',
  },
  divider: {
    backgroundColor: '#eaeaea',
    height: 0.5,
    marginHorizontal: 19,
    marginVertical: 16,
  },
  relatedArticleText: {
    color: '#222222',
    fontSize: 16,
    fontWeight: '500',
    lineHeight: 22,
    marginBottom: 16,
    marginStart: 19,
    marginTop: 24,
  },
});

const DetailPageHeader = forwardRef<View, DetailPageHeaderProps>(
  ({ model, onActionAreaLayout }, actionAreaRef) => {
    return (
      <View style={styles.container}>
        {/* 详情卡片 */}
        <DetailCard model={model} />

        {/* 操作功能区 */}
        <View ref={actionAreaRef} style={styles.actionArea} onLayout={onActionAreaLayout}>
          <DetailActionView model={model} isSplitEqually={false} />
        </View>

        {/* 分割线 */}
        <View style={styles.divider} />

        {/* 相关作品标题 */}
        <Text style={styles.relatedArticleText}>相关作品</Text>
      </View>
    );
  },
);

DetailPageHeader.displayName = 'DetailPageHeader';

export default DetailPageHeader;
