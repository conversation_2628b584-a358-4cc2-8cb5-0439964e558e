import React, { useCallback } from 'react';
import { StyleSheet, Text, TouchableOpacity, View } from 'react-native';
import { AlertSheet } from '@kid-ui/krn';

export interface GuideDialogProps {
  onClose?: () => void;
}

interface RenderContentParams {
  alert: any;
  onClose?: () => void;
}

const alertStyles = StyleSheet.create({
  button: {
    backgroundColor: '#FE3666',
    borderRadius: 24,
    minWidth: 120,
    paddingHorizontal: 32,
    paddingVertical: 12,
  },
  buttonText: {
    color: '#ffffff',
    fontSize: 16,
    fontWeight: '500',
    textAlign: 'center',
  },
  container: {
    alignItems: 'center',
    paddingVertical: 20,
  },
  image: {
    alignItems: 'center',
    backgroundColor: '#f0f0f0',
    borderRadius: 60,
    height: 120,
    justifyContent: 'center',
    marginBottom: 20,
    width: 120,
  },
  imagePlaceholder: {
    color: '#999',
    fontSize: 40,
  },
  subtitle: {
    color: '#666666',
    fontSize: 14,
    lineHeight: 20,
    marginBottom: 24,
    textAlign: 'center',
  },
  title: {
    color: '#222222',
    fontSize: 18,
    fontWeight: '600',
    marginBottom: 8,
    textAlign: 'center',
  },
});

const AlertContent: React.FC<RenderContentParams> = ({ alert, onClose }) => {
  const handleButtonPress = useCallback(() => {
    console.log('按钮被点击了');
    if (onClose) {
      onClose();
    }
    if (alert && alert.close) {
      alert.close();
    }
  }, [alert, onClose]);

  return (
    <View style={alertStyles.container}>
      {/* 图片 */}
      <View style={alertStyles.image}>
        <Text style={alertStyles.imagePlaceholder}>📷</Text>
      </View>

      {/* 第一段文字 */}
      <Text style={alertStyles.title}>欢迎使用我们的应用</Text>

      {/* 第二段文字 */}
      <Text style={alertStyles.subtitle}>
        这里是详细的描述信息，可以包含多行文本内容，为用户提供更多的说明和指导。
      </Text>

      {/* 按钮 */}
      <TouchableOpacity style={alertStyles.button} onPress={handleButtonPress}>
        <Text style={alertStyles.buttonText}>确定</Text>
      </TouchableOpacity>
    </View>
  );
};

export const GuideDialog = {
  show(props: GuideDialogProps = {}) {
    const alert = AlertSheet({
      renderContent: () => <AlertContent alert={alert} {...props} />,
      showMask: true,
      wrapStyle: {
        borderRadius: 12,
        backgroundColor: '#ffffff',
        paddingHorizontal: 20,
        paddingTop: 20,
        paddingBottom: 20,
      },
    });
    return alert;
  },
};

export default GuideDialog;
