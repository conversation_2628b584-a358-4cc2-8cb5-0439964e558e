import { type UsePageDataParams } from '@mfe/plus';

export type IndexData = {
  id: string;
  componentName: string;
  time: string;
};

export type IndexParams = {
  componentName: string;
};

export const getIndexData: UsePageDataParams<IndexData, IndexParams> = ({ initialProps }) => ({
  requestOptions: {
    method: 'GET',
    url: `/path1/${initialProps.bundleId}`,
    params: { componentName: initialProps.componentName },
  },
});
