import type { PageConfig, RouterConfig } from '@mfe/plus';
import { getIndexData } from './apis';
import { PageList } from './constants';

// 页面参数定义配置
export type Pages = PageConfig<{
  [PageList.Index]: {
    data: ReturnType<typeof getIndexData>; // 接口请求定义，没有接口请求可不写 data 参数
    // dataExtra: undefined; // 接口请求需要携带的额外参数（比如页码之类的，在 usePage 时指定）
    // router?: {}; // 页面导航需要的参数，可用于目标页面传参使用，没有参数可不写 router 参数
  };
  [PageList.Page2]: NonNullable<unknown>;
}>;

export const router: RouterConfig<Pages> = {
  pages: {
    [PageList.Index]: {
      component: () => require('./pages/home').default,
      usePageDataParams: getIndexData,
    },
    [PageList.Page2]: {
      component: () => require('./pages/detail').default,
    },
  },
};
