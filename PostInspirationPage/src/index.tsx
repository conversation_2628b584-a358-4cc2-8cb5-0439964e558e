import '@kds/react-native-gesture-handler/createHandler';
import { AppRegistry } from 'react-native';
import { ErrorBoundaryWrapper } from '@lux/krn-error-boundary';
import {
  PlusRouteApp,
  RequestHostMode,
  ResponseWithDataField,
  setBundleRequestHost,
  setDefaultResponseValidator,
  setLogRecoveryBundleID,
} from '@mfe/plus';

// 日志标记
setLogRecoveryBundleID('KwaiPostInspiration');

// 设置请求域名
setBundleRequestHost(RequestHostMode.BusinessName, 'api');

// 设置服务端接口字段格式
setDefaultResponseValidator(ResponseWithDataField);

// 启用本地 mock
__DEV__ && require('@mfe/plus').debugEnableRequestMock(require('../__mocks__'));

AppRegistry.registerComponent(
  'main',
  PlusRouteApp({
    coPage: true,
    // pvConfig: {
    //   page: '',
    //   params: ({ initialProps }) => ({}),
    // },
    getRouter: () => require('./router').router,
    Wrapper: ErrorBoundaryWrapper,
    // Wrapper: App => ErrorBoundaryWrapper(App, { backgroundColor: '#000' }),
  }),
);
