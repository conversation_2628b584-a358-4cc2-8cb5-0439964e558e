{"krnBaseInfo": {"businessLine": {"name": "生产", "value": 8, "children": []}, "business": "common", "businessName": "api", "navigation": "multi", "bundleId": "KwaiPostInspiration"}, "krnBundleInfo": {"bundleId": "KwaiPostInspiration"}, "id": "h77bq45gbzl", "projectName": "PostInspirationPage", "projectChineseName": "生产创作灵感页", "template": {"id": 2, "name": "KRN", "platform": "H5", "framework": "KRN", "plugins": [{"name": "@gundam/gundam-plugin-krn-template", "description": "gundam krn模板生成", "type": "<PERSON><PERSON><PERSON>", "framework": ["KRN"], "platform": ["KRN"], "domain": "template", "tags": ["KRN", "generate"], "docs": "krn-template", "contributors": ["ma<PERSON>an", "dongyunlong", "yumingming", "jin<PERSON>ing", "<PERSON><PERSON><PERSON><PERSON>"]}, {"name": "@gundam/krn-plugin-plus-style", "description": "KRN PlusStyle 插件", "type": "Provider", "framework": ["KRN"], "platform": ["KRN"], "domain": "style", "tags": ["KRN", "样式", "大屏适配"], "docs": "plus-style-krn", "contributors": ["jin<PERSON>ing"]}, {"name": "@gundam/gundam-plugin-gitlab", "description": "创建gitlab仓库", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "develop", "tags": ["仓库"], "docs": "gitlab", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "<PERSON><PERSON>junhe", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-keep", "description": "自动创建krn bundle插件", "type": "<PERSON><PERSON><PERSON>", "framework": ["KRN"], "platform": ["H5"], "domain": "standard", "tags": ["KRN", "规范"], "docs": "keep", "contributors": ["ma<PERSON>an", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-radar", "description": "自动创建雷达项目", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "operation", "tags": ["radar", "监控"], "docs": "radar", "contributors": ["<PERSON><PERSON><PERSON><PERSON>", "ma<PERSON>an", "<PERSON><PERSON>junhe", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-kfc", "description": "创建 kfc 应用插件", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "standard", "tags": ["规范"], "docs": "kfc", "contributors": ["<PERSON><PERSON>junhe", "ma<PERSON>an", "<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-kdev", "description": "kdev CD流水线创建插件", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "standard", "tags": ["CI", "CD", "流水线"], "docs": "kdev", "contributors": ["ma<PERSON>an", "<PERSON><PERSON><PERSON><PERSON>", "dongyunlong", "yumingming"]}, {"name": "@gundam/gundam-plugin-api-generator", "description": "根据 swagger3 接口文档生成 interface 和 service 请求文件", "type": "<PERSON><PERSON><PERSON>", "framework": ["Vue2", "Vue3"], "platform": ["H5", "PC"], "domain": "mock", "tags": ["请求", "效率"], "docs": "api-generator", "contributors": ["chenzihao03", "<PERSON><PERSON><PERSON><PERSON>", "dongyunlong"]}, {"name": "@gundam/gundam-plugin-hyper-kds", "description": "自动接入KRN hyper sdk", "type": "Kds", "framework": ["KRN"], "platform": ["KRN"], "domain": "hyper", "tags": ["KRN", "性能检测"], "docs": "hyper-kds", "contributors": ["ma<PERSON>an"]}], "type": "Git", "uri": "*************************:mfe/platform/gundam/template-krn.git", "description": "主站KRN模板", "env": {"KFC_TEMPLATE_ID": 43, "GUNDAM_WORKSPACE_URL": "*************************:mfe/platform/gundam/template-krn-workspace.git"}}, "serviceTreeNode": {"name": "Public（公用节点）", "value": 81469, "path": "/kuaishou/webservice/frontend/mfe/public"}, "plugins": ["@gundam/gundam-plugin-krn-template", "@gundam/krn-plugin-plus-style", "@gundam/gundam-plugin-gitlab", "@gundam/gundam-plugin-keep", "@gundam/gundam-plugin-radar", "@gundam/gundam-plugin-kfc", "@gundam/gundam-plugin-kdev", "@gundam/gundam-plugin-api-generator", "@gundam/gundam-plugin-hyper-kds"]}