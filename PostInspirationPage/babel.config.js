const typescriptConfig = {
  allowNamespaces: true,
  optimizeConstEnums: true,
};

module.exports = {
  // 自定义插件
  plugins: [
    [
      'module-resolver',
      {
        root: ['.'],
        extensions: [
          '.ios.ts',
          '.android.ts',
          '.ts',
          '.ios.tsx',
          '.android.tsx',
          '.tsx',
          '.jsx',
          '.js',
          '.json',
        ],
        alias: {
          // 支持 @/ 表示 src 目录路径映射
          '@': './src',
          // Lux 相关包以源码形式引入
          '^@lux/[^/]+$': '\\0/base/src',
          '^(@lux/[^/]+)/lib/(?:commonjs|module)(/.*)?$': '\\1/base/src\\2',
          // Fix 常见包 webpack 打包失败的问题，引用 ESM
          '^@babel/runtime/helpers/esm/(.+)(.js)?$': ([, name, ext]) =>
            ['inheritsLoose', 'asyncToGenerator'].includes(name)
              ? `@babel/runtime/helpers/esm/${name}${ext || '.js'}`
              : `@babel/runtime/helpers/${name}${ext || '.js'}`,
          '^@babel/runtime/helpers/(.+)(?<!.js)$': `@babel/runtime/helpers/\\1.js`,
          '^@babel/runtime/regenerator$': '@babel/runtime/regenerator/index.js',
          '@kds/react-native-swiper': '@kds/react-native-swiper/src',
          '^@ks/weblogger/lib/(.+)': '@ks/weblogger/es/\\1',
          lodash: 'lodash-es',
          'lodash/**': 'lodash-es/**',
          'react-native-gesture-handler': '@kds/react-native-gesture-handler',
          '^react-native-gesture-handler/src/(.+)': '@kds/react-native-gesture-handler/\\1',
          '^react-native-gesture-handler/(.+)': '@kds/react-native-gesture-handler/\\1',
          'react-native-image-pan-zoom': 'react-native-image-pan-zoom/src',
          'react-native-image-zoom-viewer': 'react-native-image-zoom-viewer/src',
          'react-native-swiper': 'react-native-swiper/src',
          'react-native-svg': '@kds/react-native-svg',
          '^react-native-svg/lib/module/(.+)': '@kds/react-native-svg/src/\\1',
          'redux-logger': 'redux-logger/src',
          '^use-sync-external-store/([^/]+)$': ([package, name]) =>
            name === 'shim' ? package : `use-sync-external-store/shim/${name}`,
          '^zustand(/.+)?$': 'zustand/esm\\1',
        },
      },
    ],
    // 支持 babel 宏代码
    '@mfe/macros',
  ],

  // babel 阶段避免压缩，避免部分内联影响 tree-shaking
  compact: false,
  // babel 阶段保留注释，避免 /*#__PURE__*/ 标记被删除而导致部分 tree-shaking 失败
  comments: true,
  presets: [
    // React Native 预设
    'module:metro-react-native-babel-preset',
  ],
  env: {
    production: {
      plugins: [
        // 生产环境移除 console.log 语句
        ['transform-remove-console', { exclude: ['error'] }],
      ],
    },
  },
  overrides: [
    {
      // 不支持 ES Module，配置将其单独转为 commonjs
      test: filename =>
        [
          '@canvas/image-data',
          '@kds/cameraroll',
          '@kds/react-native-bindingx',
          '@kds/react-native-blur',
          '@kds/react-native-gesture-handler/GestureComponents',
          '@mzvonar/setin',
          '@react-native-community/blur',
          '@react-native-community/cameraroll',
          '@react-native-community/viewpager',
          'color-convert',
          'css-to-react-native',
          'css-tree',
          'dayjs',
          'dom-serializer',
          'ks-apple-map',
          'object-assign',
          'query-string',
          'react-native-blur',
          'react-native-fs',
          'react-native-gesture-handler/GestureComponents',
          'react-native-screens',
          'react-native-scrollable-tab-view',
          'recyclerlistview',
          'whatwg-url-without-unicode',
        ].some(packageName => filename.includes(`node_modules/${packageName}`)),
      plugins: [
        [require('@babel/plugin-proposal-export-default-from')],
        [
          require('@babel/plugin-transform-modules-commonjs'),
          { strict: false, strictMode: false, lazy: false },
        ],
      ],
    },
    {
      // 配置 ts 文件启用 optimizeConstEnums 配置
      test: filename => filename.endsWith('.ts'),
      plugins: [
        [require('@babel/plugin-transform-typescript'), { ...typescriptConfig, isTSX: false }],
      ],
    },
    {
      // 配置 tsx 文件启用 optimizeConstEnums 配置
      test: filename => filename.endsWith('.tsx'),
      plugins: [
        [require('@babel/plugin-transform-typescript'), { ...typescriptConfig, isTSX: true }],
      ],
    },
  ],
};
