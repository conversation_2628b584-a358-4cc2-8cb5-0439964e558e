import type { LocalMock } from '@mfe/plus';

export const api: LocalMock = {
  // params 为接口请求参数，pathParams 为路径中的参数，可以自行决定引入 mockjs 之类的库
  'GET /path1/:id': (params, pathParams) => ({
    result: 1,
    data: {
      id: pathParams.id,
      componentName: params.componentName,
      time: new Date().toLocaleString(),
      nested: params.foo?.[0]?.bar,
    },
  }),
  // 支持返回 Promise，reject 可引发请求报错
  'GET /path2': () =>
    Promise.resolve({
      result: 1,
      data: {
        alice: 'bob',
      },
    }),
  // 不需要获取参数的话，也可以直接使用对象定义
  'GET /path3': {
    result: 1,
    data: {
      foo: 'bar',
    },
  },
  // 指定 undefined 或结果返回 undefined 将此接口请求放行，请求服务端接口
  'GET /path4': undefined,
  'GET /path5': () => undefined,
  'GET /path6': () => Promise.resolve(undefined),
  // 未指定的接口 / 未命中的接口将返回 404
};
