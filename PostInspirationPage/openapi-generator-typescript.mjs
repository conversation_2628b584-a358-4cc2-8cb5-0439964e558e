// @ts-check
/**
 * @type {import('@pex/openapi-generator-typescript').UserConfig}
 */
export default {
  // Swagger文档列表
  documents: [
    {
      /** 存储相对路径文件夹名 */
      namespace: 'GundamApp',
      /** Swagger远端地址 */
      api_doc_url: 'https://gundam-server.corp.kuaishou.com/api-docs-json',
      /** Swagger文档快照相对地址 */
      api_doc_snapshot_path: 'schemas/api-docs/gundam-app.json',
    },
  ],
  plugins: [
    {
      path: '@pex/openapi-generator-typescript/plugins/unique-operation-id',
    },
    {
      path: '@gundam/api-plus-client',
    },
  ],
  /** API文件生成相对地址 */
  targetDir: 'src/services/open-api-docs',
};
