# feature.macro 配置
krn-features:
  disable:
    # 默认启用的能力，可以在这里禁用
    ### ========== 🔥 通用 🔥 ========== ###
    # 提供 URLSearchParams 标准化修复 patch
    # - mfe/plus/patch/URLSearchParams
  enable:
    # 默认禁用的能力，可以在这里启用
    ### ========== 🔥 通用 🔥 ========== ###
    # Weblogger Debug 输出（仅 开发环境 下生效）
    - mfe/plus/weblogger/debug

    ### ========== 🌐 全局状态管理库 🌐 ========== ###
    # 提供基于 Redux Toolkit 的兼容共享引擎的全局状态管理能力（需安装依赖 @reduxjs/toolkit react-redux）
    # - mfe/plus/feature/reduxToolkit
    # 提供基于 Rematch 的兼容共享引擎的全局状态管理能力（需安装依赖 @rematch/core react-redux redux）
    # - mfe/plus/feature/rematch

    ### ========== 🎬 直播间场景 【直播间容器建议全开】 🎬 ========== ###
    # 直播间场景下，半屏页面上报 PV 后，退出直播间时解决埋点漂移问题
    # - mfe/plus/weblogger/exitReportLivePV
    # 修复双端 props 不统一问题
    # - mfe/plus/initialProps/fixProps

# Gundam 插件配置
gundam-plugins:
  - "@gundam/krn-plugin-plus-style"
