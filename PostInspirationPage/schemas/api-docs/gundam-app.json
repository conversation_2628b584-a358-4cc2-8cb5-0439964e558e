{"openapi": "3.0.0", "paths": {"/swagger-demo/hello": {"get": {"operationId": "SwaggerDemoController_getHello", "parameters": [], "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/HelloMessage"}}}}}}}, "/swagger-demo/double": {"post": {"operationId": "SwaggerDemoController_double", "parameters": [], "requestBody": {"required": true, "content": {"application/json": {"schema": {"$ref": "#/components/schemas/CalculateDoubleDto"}}}}, "responses": {"200": {"description": "", "content": {"application/json": {"schema": {"$ref": "#/components/schemas/DoubleNumber"}}}}}}}}, "info": {"title": "Gundam API", "description": "Gundam API description", "version": "1.0", "contact": {}}, "tags": [{"name": "Gundam", "description": "Demo"}], "servers": [], "components": {"schemas": {"HelloMessage": {"type": "object", "properties": {"message": {"type": "string"}}, "required": ["message"]}, "CalculateDoubleDto": {"type": "object", "properties": {"base": {"type": "number"}}, "required": ["base"]}, "DoubleNumber": {"type": "object", "properties": {"result": {"type": "number"}}, "required": ["result"]}}}}