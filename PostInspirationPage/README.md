![Logo](https://static.yximgs.com/udata/pkg/kuaishou-ad-frontend-cdn/jinliming2/GundamKRN.png)

# Gundam KRN

主站前端应用脚手架创建项目

## 相关介绍

[Gundam文档站](https://gundam.led.staging.kuaishou.com/)

## 本地开发

**开发环境**

使用 nvm 管理 node 版本，建议使用 node@18.20 与流水线默认版本保持一致

```bash
nvm use v18.20
```

包管理工具使用 pnpm

```bash
corepack enable pnpm
```

**运行项目**

安装依赖

```bash
pnpm
```

启动项目

```bash
# 默认使用 Webpack 启动
pnpm start
# 若想切回 Metro，需要将包管理切换成 yarn
```

**Bundle 打包**

Bundle 打包统一使用 [KeeP-KDS](https://keep.corp.kuaishou.com/kds-web/kds/kds-publish-list)。

> 注意请开启 Webpack 构建，否则包体积会较大。
